import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { <PERSON>risma } from "@axa/database-medical";
import { MatchInitiator, MatchStatus, PayType } from "@axa/database-medical";
import { calculateSkip } from "@axa/lib/utils";

import type { ProcedureResult } from "../../../types/select";

import { ActionType, ResourceType } from "../../../constants/actions";
import { performAction } from "../../../lib/actions";
import { authorizedProcedure, createTRPCRouter } from "../../../trpc";
import { authorization } from "../../../utils/authorization";

// Base enums
const zMatchStatus = z.nativeEnum(MatchStatus);
const zMatchInitiator = z.nativeEnum(MatchInitiator);

// Core schema (direct fields)
const zCoreMatch = z.object({
  status: zMatchStatus.default("PENDING"),
  initiator: zMatchInitiator.default("PROVIDER"),
  initiationNote: z.string().optional(),
});

// Extended schema with relations
const zMatchSchema = zCoreMatch.extend({
  jobId: z.string(),
  providerId: z.string(),
  organizationId: z.string(),
  applicationId: z.string().optional(),
  offerId: z.string().optional(),
});

// Schema for controlling nested selections
const zIncludeSchema = z
  .object({
    job: z.boolean().default(false),
    organization: z.boolean().default(false),
    provider: z.boolean().default(false),
    application: z.boolean().default(false),
    offer: z.boolean().default(false),
    position: z.boolean().default(false),
    contract: z.boolean().default(false),
    compensation: z.boolean().default(false),
    thread: z.boolean().default(false),
    steps: z.boolean().default(false),
    actions: z.boolean().default(false),
  })
  .optional();

// List schema for pagination and filtering
const zListSchema = z.object({
  query: z.string().optional(),
  pageSize: z.number().optional(),
  pageNumber: z.number().optional(),
  status: zMatchStatus.optional(),
  initiator: zMatchInitiator.optional(),
  jobId: z.string().optional(),
  providerId: z.string().optional(),
  organizationId: z.string().optional(),
  include: zIncludeSchema.optional(),
});

// Standard selections for all related data
const selection = {
  match: {
    id: true,
    status: true,
    initiator: true,
    initiatedAt: true,
    initiatedBy: true,
    initiationNote: true,
    createdAt: true,
    updatedAt: true,
    jobId: true,
    organizationId: true,
    providerId: true,
    applicationId: true,
    offerId: true,
    positionId: true,
    contractId: true,
    threadId: true,
  },
  job: {
    id: true,
    status: true,
    summary: true,
    role: true,
    paymentType: true,
    paymentAmount: true,
    allowRateNegotiation: true,
    minNegotiableRate: true,
    maxNegotiableRate: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
  provider: {
    id: true,
    title: true,
    person: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        avatar: true,
      },
    },
  },
  person: {
    id: true,
    firstName: true,
    lastName: true,
    avatar: true,
  },
  compensation: {
    id: true,
    minRate: true,
    maxRate: true,
    currentOfferRate: true,
    finalAgreedRate: true,
    rateStrategy: true,
    negotiationCount: true,
    negotiationStatus: true,
    lastOfferBy: true,
    offerExpiresAt: true,
    paymentType: true,
  },
  application: {
    id: true,
    status: true,
    notes: true,
  },
  offer: {
    id: true,
    status: true,
    notes: true,
  },
  position: {
    id: true,
    status: true,
    role: true,
    summary: true,
  },
  contract: {
    id: true,
    status: true,
    title: true,
  },
  thread: {
    id: true,
  },
  steps: {
    id: true,
    type: true,
    status: true,
    order: true,
    isRequired: true,
    isSkippable: true,
    isSuccessful: true,
    notes: true,
  },
} satisfies {
  match: Prisma.MatchSelect;
  job: Prisma.JobPostSelect;
  organization: Prisma.OrganizationSelect;
  provider: Prisma.ProviderSelect;
  person: Prisma.PersonSelect;
  compensation: Prisma.JobCompensationSelect;
  application: Prisma.ApplicationSelect;
  offer: Prisma.OfferSelect;
  position: Prisma.JobPositionSelect;
  contract: Prisma.ContractSelect;
  thread: Prisma.ThreadSelect;
  steps: Prisma.MatchStepSelect;
};

export const matchesRouter = createTRPCRouter({
  create: authorizedProcedure
    .input(zMatchSchema)
    .mutation(async ({ ctx, input }) => {
      // Validate organization/provider access
      await authorization({ ctx, input });

      // Validate job exists
      const job = await ctx.prisma.jobPost.findUnique({
        where: { id: input.jobId },
        select: {
          id: true,
          organizationId: true,
          summary: true,
          allowRateNegotiation: true,
          minNegotiableRate: true,
          maxNegotiableRate: true,
        },
      });

      if (!job) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job post not found",
        });
      }

      // Validate provider exists
      const provider = await ctx.prisma.provider.findUnique({
        where: { id: input.providerId },
        select: {
          id: true,
          person: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      if (!provider) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Provider not found",
        });
      }

      // Validate organization exists
      const organization = await ctx.prisma.organization.findUnique({
        where: { id: input.organizationId },
        select: { id: true, name: true },
      });

      if (!organization) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Organization not found",
        });
      }

      // Check for existing match
      const existingMatch = await ctx.prisma.match.findFirst({
        where: {
          jobId: input.jobId,
          providerId: input.providerId,
        },
        select: { id: true },
      });

      if (existingMatch) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "A match already exists for this job and provider",
        });
      }

      // Create match with proper relationships
      const result = await ctx.prisma.$transaction(async (tx) => {
        // Create the match
        const newMatch = await tx.match.create({
          data: {
            status: input.status,
            initiator: input.initiator,
            initiatedBy: ctx.user.id,
            initiationNote: input.initiationNote,
            jobId: input.jobId,
            providerId: input.providerId,
            organizationId: input.organizationId,
            applicationId: input.applicationId,
            offerId: input.offerId,
          },
          select: {
            ...selection.match,
            job: { select: selection.job },
            provider: { select: selection.provider },
            organization: { select: selection.organization },
          },
        });

        // Create compensation record if job allows rate negotiation
        if (job.allowRateNegotiation) {
          await tx.jobCompensation.create({
            data: {
              minRate: job.minNegotiableRate ?? 0,
              maxRate: job.maxNegotiableRate ?? 0,
              paymentType: PayType.HOURLY,
              matchId: newMatch.id,
              jobId: input.jobId,
              providerId: input.providerId,
              organizationId: input.organizationId,
            },
          });
        }

        return newMatch;
      });

      // Log action using performAction()
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.CREATE,
        resourceType: ResourceType.MATCH,
        resourceId: result.id,
        organizationId: input.organizationId,
        providerId: input.providerId,
        metadata: {
          title: `Match for ${result.job.summary}`,
          organizationName: result.organization.name,
          providerName: `${result.provider.person.firstName} ${result.provider.person.lastName}`,
          actorName: `${ctx.user.firstName} ${ctx.user.lastName}`,
        },
      });

      return {
        id: result.id,
        status: result.status,
        job: {
          id: result.job.id,
          summary: result.job.summary,
        },
        provider: {
          id: result.provider.id,
          person: {
            firstName: result.provider.person.firstName,
            lastName: result.provider.person.lastName,
          },
        },
        organization: {
          id: result.organization.id,
          name: result.organization.name,
        },
      };
    }),

  get: authorizedProcedure
    .input(z.object({ id: z.string(), include: zIncludeSchema.optional() }))
    .query(async ({ ctx, input }) => {
      const select = {
        ...selection.match,
        job: input.include?.job ? { select: selection.job } : undefined,
        organization: input.include?.organization
          ? { select: selection.organization }
          : undefined,
        provider: input.include?.provider
          ? { select: selection.provider }
          : undefined,
        compensation: input.include?.compensation
          ? { select: selection.compensation }
          : undefined,
        application: input.include?.application
          ? { select: selection.application }
          : undefined,
        offer: input.include?.offer ? { select: selection.offer } : undefined,
        position: input.include?.position
          ? { select: selection.position }
          : undefined,
        contract: input.include?.contract
          ? { select: selection.contract }
          : undefined,
        thread: input.include?.thread
          ? { select: selection.thread }
          : undefined,
        steps: input.include?.steps
          ? { select: selection.steps, orderBy: { order: "asc" } }
          : undefined,
      } satisfies Prisma.MatchSelect;

      const match = (await ctx.prisma.match.findUnique({
        where: { id: input.id },
        select,
      })) as unknown as ProcedureResult<
        typeof select,
        Prisma.$MatchPayload
      > | null;

      if (!match) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Match not found",
        });
      }

      return match;
    }),

  list: authorizedProcedure.input(zListSchema).query(async ({ ctx, input }) => {
    // Apply authorization filters
    const auth = await authorization({ ctx, input });

    // Build the where clause
    const where: Prisma.MatchWhereInput = {
      status: input.status,
      initiator: input.initiator,
      jobId: input.jobId,
      providerId: input.providerId ?? auth.provider ?? undefined,
      organizationId:
        input.organizationId ??
        (auth.organizations?.length ? { in: auth.organizations } : undefined),
    };

    // Add search conditions
    if (input.query) {
      const searchConditions: Prisma.MatchWhereInput[] = [];

      if (input.include?.job) {
        searchConditions.push({
          job: {
            summary: { contains: input.query, mode: "insensitive" },
          },
        });
      }

      if (input.include?.provider) {
        searchConditions.push({
          provider: {
            person: {
              OR: [
                { firstName: { contains: input.query, mode: "insensitive" } },
                { lastName: { contains: input.query, mode: "insensitive" } },
              ],
            },
          },
        });
      }

      if (input.include?.organization) {
        searchConditions.push({
          organization: {
            name: { contains: input.query, mode: "insensitive" },
          },
        });
      }

      if (searchConditions.length > 0) {
        where.OR = searchConditions;
      }
    }

    // Build the select clause
    const select = {
      ...selection.match,
      job: input.include?.job ? { select: selection.job } : undefined,
      organization: input.include?.organization
        ? { select: selection.organization }
        : undefined,
      provider: input.include?.provider
        ? { select: selection.provider }
        : undefined,
      compensation: input.include?.compensation
        ? { select: selection.compensation }
        : undefined,
      application: input.include?.application
        ? { select: selection.application }
        : undefined,
      offer: input.include?.offer ? { select: selection.offer } : undefined,
      position: input.include?.position
        ? { select: selection.position }
        : undefined,
      contract: input.include?.contract
        ? { select: selection.contract }
        : undefined,
      thread: input.include?.thread ? { select: selection.thread } : undefined,
      steps: input.include?.steps
        ? { select: selection.steps, orderBy: { order: "asc" } }
        : undefined,
    } satisfies Prisma.MatchSelect;

    // Execute queries
    const [total, rawItems] = await Promise.all([
      ctx.prisma.match.count({ where }),
      ctx.prisma.match.findMany({
        where,
        skip: calculateSkip({
          pageSize: input.pageSize,
          pageNumber: input.pageNumber,
        }),
        take: input.pageSize,
        orderBy: { createdAt: "desc" },
        select,
      }),
    ]);

    const items = rawItems as unknown as ProcedureResult<
      typeof select,
      Prisma.$MatchPayload
    >[];

    return {
      items,
      total,
    };
  }),

  update: authorizedProcedure
    .input(
      z.object({
        id: z.string(),
        data: zCoreMatch.partial(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get match details for authorization and logging
      const match = await ctx.prisma.match.findUnique({
        where: { id: input.id },
        select: {
          id: true,
          organizationId: true,
          providerId: true,
          status: true,
          job: {
            select: {
              id: true,
              summary: true,
            },
          },
          provider: {
            select: {
              id: true,
              person: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          organization: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!match) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Match not found",
        });
      }

      // Apply authorization
      await authorization({
        ctx,
        input: {
          providerId: match.providerId,
          organizationId: match.organizationId,
        },
      });

      // Build update data
      const updateData: Prisma.MatchUpdateInput = {};

      if (input.data.status !== undefined) {
        updateData.status = input.data.status;
      }

      if (input.data.initiator !== undefined) {
        updateData.initiator = input.data.initiator;
      }

      if (input.data.initiationNote !== undefined) {
        updateData.initiationNote = input.data.initiationNote;
      }

      if (Object.keys(updateData).length === 0) {
        return { id: input.id };
      }

      // Update match
      await ctx.prisma.match.update({
        where: { id: input.id },
        data: updateData,
      });

      // Get updated status for logging
      const updatedStatus = input.data.status ?? match.status;

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.UPDATE,
        resourceType: ResourceType.MATCH,
        resourceId: input.id,
        organizationId: match.organizationId,
        providerId: match.providerId,
        metadata: {
          title: `Match for ${match.job.summary}`,
          organizationName: match.organization.name,
          providerName: `${match.provider.person.firstName} ${match.provider.person.lastName}`,
          actorName: `${ctx.user.firstName} ${ctx.user.lastName}`,
          status: updatedStatus,
        },
      });

      return { id: input.id };
    }),

  delete: authorizedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Get match details for authorization and logging
      const match = await ctx.prisma.match.findUnique({
        where: { id: input.id },
        select: {
          id: true,
          organizationId: true,
          providerId: true,
          job: {
            select: {
              id: true,
              summary: true,
            },
          },
          provider: {
            select: {
              id: true,
              person: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          organization: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!match) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Match not found",
        });
      }

      // Apply authorization
      await authorization({
        ctx,
        input: {
          providerId: match.providerId,
          organizationId: match.organizationId,
        },
      });

      // Delete match (this will cascade to related entities like compensation)
      await ctx.prisma.match.delete({
        where: { id: input.id },
      });

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.DELETE,
        resourceType: ResourceType.MATCH,
        resourceId: input.id,
        organizationId: match.organizationId,
        providerId: match.providerId,
        metadata: {
          title: `Match for ${match.job.summary}`,
          organizationName: match.organization.name,
          providerName: `${match.provider.person.firstName} ${match.provider.person.lastName}`,
          actorName: `${ctx.user.firstName} ${ctx.user.lastName}`,
        },
      });

      return { id: input.id };
    }),
});
