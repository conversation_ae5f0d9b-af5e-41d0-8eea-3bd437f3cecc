{"extends": "@axa/tsconfig/internal-package.json", "noErrorTruncation": true, "compilerOptions": {"noErrorTruncation": true, "jsx": "preserve", "outDir": "dist", "tsBuildInfoFile": "node_modules/.cache/tsbuildinfo.json", "rootDir": "../..", "baseUrl": ".", "paths": {"@/ui/*": ["../ui/src/*"]}}, "include": ["src/**/*.ts", "src/types.d.ts", "__tests__/rateNegotiation.test.ts"], "exclude": ["node_modules"]}