/*
  Warnings:

  - A unique constraint covering the columns `[email]` on the table `Referral` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[payoutId]` on the table `Referral` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[matchId]` on the table `Thread` will be added. If there are existing duplicate values, this will fail.

*/
-- <PERSON><PERSON>Enum
CREATE TYPE "public"."MatchStatus" AS ENUM ('PENDING', 'VALIDATING', 'NEGOTIATING', 'FINALIZING', 'MATCHED', 'WITHDRAWN', 'DECLINED', 'EXPIRED', 'CANCELLED');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."MatchInitiator" AS ENUM ('PROVIDER', 'ORGANIZATION', 'MUTUAL', 'REFERRAL');

-- CreateEnum
CREATE TYPE "public"."StepType" AS ENUM ('IDENTITY_VERIFICATION', 'BACKGROUND_CHECK', 'RATE_NEGOTIATION', 'INTERVIEW', 'SCHEDULE_NEGOTIATION', 'BENEFITS_NEGOTIATION', 'CONTRACT_TERMS', 'REFERENCE_CHECK', 'SKILLS_ASSESSMENT', 'DRUG_SCREENING', 'CREDENTIAL_VERIFICATION', 'FINAL_APPROVAL', 'ONBOARDING_PREP', 'EQUIPMENT_ASSIGNMENT', 'ORIENTATION_SCHEDULING');

-- CreateEnum
CREATE TYPE "public"."StepStatus" AS ENUM ('PENDING', 'VALIDATING', 'IN_PROGRESS', 'COMPLETED', 'SKIPPED', 'FAILED', 'CANCELLED');

-- AlterEnum
ALTER TYPE "public"."IncidentStatus" ADD VALUE 'CLOSED';

-- DropIndex
DROP INDEX "public"."action_idx";

-- DropIndex
DROP INDEX "public"."thread_idx";

-- AlterTable
ALTER TABLE "public"."Action" ADD COLUMN     "matchId" TEXT,
ADD COLUMN     "matchStepId" TEXT;

-- AlterTable
ALTER TABLE "public"."JobPost" ADD COLUMN     "allowRateNegotiation" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "finalNegotiatedRate" DOUBLE PRECISION,
ADD COLUMN     "maxNegotiableRate" DOUBLE PRECISION,
ADD COLUMN     "minNegotiableRate" DOUBLE PRECISION;

-- AlterTable
ALTER TABLE "public"."Referral" ALTER COLUMN "status" SET DEFAULT 'INVITED';

-- AlterTable
ALTER TABLE "public"."Thread" ADD COLUMN     "matchId" TEXT;

-- CreateTable
CREATE TABLE "public"."Match" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "status" "public"."MatchStatus" NOT NULL DEFAULT 'PENDING',
    "initiator" "public"."MatchInitiator" NOT NULL DEFAULT 'PROVIDER',
    "initiatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "initiatedBy" TEXT NOT NULL,
    "initiationNote" TEXT,
    "jobId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "applicationId" TEXT,
    "offerId" TEXT,
    "positionId" TEXT,
    "contractId" TEXT,
    "threadId" TEXT,

    CONSTRAINT "Match_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."MatchStep" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "startedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "type" "public"."StepType" NOT NULL DEFAULT 'RATE_NEGOTIATION',
    "status" "public"."StepStatus" NOT NULL DEFAULT 'PENDING',
    "order" INTEGER NOT NULL DEFAULT 1,
    "isRequired" BOOLEAN NOT NULL DEFAULT true,
    "isSkippable" BOOLEAN NOT NULL DEFAULT false,
    "skipReason" TEXT,
    "isSuccessful" BOOLEAN,
    "notes" TEXT,
    "metadata" JSONB,
    "matchId" TEXT NOT NULL,

    CONSTRAINT "MatchStep_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."JobCompensation" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "minRate" DOUBLE PRECISION NOT NULL,
    "maxRate" DOUBLE PRECISION NOT NULL,
    "currentOfferRate" DOUBLE PRECISION,
    "finalAgreedRate" DOUBLE PRECISION,
    "rateStrategy" TEXT,
    "negotiationCount" INTEGER NOT NULL DEFAULT 0,
    "paymentType" "public"."PayType" NOT NULL DEFAULT 'HOURLY',
    "baseRate" DOUBLE PRECISION,
    "nightRateMultiplier" DOUBLE PRECISION DEFAULT 1.25,
    "overtimeRateMultiplier" DOUBLE PRECISION DEFAULT 1.5,
    "holidayRateMultiplier" DOUBLE PRECISION DEFAULT 2.0,
    "callbackRate" DOUBLE PRECISION,
    "orientationRate" DOUBLE PRECISION,
    "supervisionRate" DOUBLE PRECISION,
    "totalMonthlyValue" DOUBLE PRECISION,
    "effectiveHourlyRate" DOUBLE PRECISION,
    "negotiationStatus" TEXT,
    "lastOfferBy" TEXT,
    "offerExpiresAt" TIMESTAMP(3),
    "matchId" TEXT,
    "jobId" TEXT NOT NULL,
    "providerId" TEXT,
    "organizationId" TEXT,

    CONSTRAINT "JobCompensation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Match_applicationId_key" ON "public"."Match"("applicationId");

-- CreateIndex
CREATE UNIQUE INDEX "Match_offerId_key" ON "public"."Match"("offerId");

-- CreateIndex
CREATE UNIQUE INDEX "Match_positionId_key" ON "public"."Match"("positionId");

-- CreateIndex
CREATE UNIQUE INDEX "Match_contractId_key" ON "public"."Match"("contractId");

-- CreateIndex
CREATE UNIQUE INDEX "Match_threadId_key" ON "public"."Match"("threadId");

-- CreateIndex
CREATE INDEX "match_initiator_status_idx" ON "public"."Match"("initiator", "status");

-- CreateIndex
CREATE INDEX "match_created_at_idx" ON "public"."Match"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "Match_jobId_providerId_key" ON "public"."Match"("jobId", "providerId");

-- CreateIndex
CREATE INDEX "match_step_order_idx" ON "public"."MatchStep"("matchId", "order");

-- CreateIndex
CREATE INDEX "step_type_status_idx" ON "public"."MatchStep"("type", "status");

-- CreateIndex
CREATE UNIQUE INDEX "MatchStep_matchId_type_key" ON "public"."MatchStep"("matchId", "type");

-- CreateIndex
CREATE UNIQUE INDEX "JobCompensation_matchId_key" ON "public"."JobCompensation"("matchId");

-- CreateIndex
CREATE INDEX "job_compensation_idx" ON "public"."JobCompensation"("matchId", "jobId");

-- CreateIndex
CREATE INDEX "compensation_strategy_idx" ON "public"."JobCompensation"("rateStrategy");

-- CreateIndex
CREATE INDEX "action_idx" ON "public"."Action"("actorId", "organizationId", "jobId", "shiftId", "locationId", "providerId", "offerId", "applicationId", "contractId", "qualificationId", "documentId", "buildingId", "departmentId", "reviewId", "invoiceId", "jobExperienceId", "scheduleId", "contactId", "incidentId", "providerVerificationId", "organizationSettingId", "matchId", "matchStepId");

-- CreateIndex
CREATE UNIQUE INDEX "Referral_email_key" ON "public"."Referral"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Referral_payoutId_key" ON "public"."Referral"("payoutId");

-- CreateIndex
CREATE UNIQUE INDEX "Thread_matchId_key" ON "public"."Thread"("matchId");

-- CreateIndex
CREATE INDEX "thread_idx" ON "public"."Thread"("shiftId", "applicationId", "offerId", "jobId", "matchId");

-- AddForeignKey
ALTER TABLE "public"."Thread" ADD CONSTRAINT "Thread_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES "public"."Match"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Action" ADD CONSTRAINT "Action_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES "public"."Match"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Action" ADD CONSTRAINT "Action_matchStepId_fkey" FOREIGN KEY ("matchStepId") REFERENCES "public"."MatchStep"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Match" ADD CONSTRAINT "Match_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."JobPost"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Match" ADD CONSTRAINT "Match_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Match" ADD CONSTRAINT "Match_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "public"."Provider"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Match" ADD CONSTRAINT "Match_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "public"."Application"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Match" ADD CONSTRAINT "Match_offerId_fkey" FOREIGN KEY ("offerId") REFERENCES "public"."Offer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Match" ADD CONSTRAINT "Match_positionId_fkey" FOREIGN KEY ("positionId") REFERENCES "public"."JobPosition"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Match" ADD CONSTRAINT "Match_contractId_fkey" FOREIGN KEY ("contractId") REFERENCES "public"."Contract"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."MatchStep" ADD CONSTRAINT "MatchStep_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES "public"."Match"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."JobCompensation" ADD CONSTRAINT "JobCompensation_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES "public"."Match"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."JobCompensation" ADD CONSTRAINT "JobCompensation_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."JobPost"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."JobCompensation" ADD CONSTRAINT "JobCompensation_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "public"."Provider"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."JobCompensation" ADD CONSTRAINT "JobCompensation_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;
